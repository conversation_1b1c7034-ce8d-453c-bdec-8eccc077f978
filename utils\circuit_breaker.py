"""
Circuit Breaker Pattern Implementation
Production-grade resilience for external dependencies

Prevents cascading failures and provides graceful degradation
when external services (database, cache, APIs) become unavailable.
"""
import asyncio
import time
import logging
from enum import Enum
from typing import Dict, Any, Optional, Callable, Awaitable
from dataclasses import dataclass
from datetime import datetime, timedelta

from utils.structured_logging import structured_logger

logger = logging.getLogger(__name__)

class CircuitState(Enum):
    """Circuit breaker states"""
    CLOSED = "closed"      # Normal operation
    OPEN = "open"          # Failing, rejecting requests
    HALF_OPEN = "half_open"  # Testing if service recovered

@dataclass
class CircuitBreakerConfig:
    """Circuit breaker configuration"""
    failure_threshold: int = 5          # Failures before opening
    recovery_timeout: float = 60.0      # Seconds before trying half-open
    success_threshold: int = 3          # Successes to close from half-open
    timeout: float = 30.0               # Operation timeout
    expected_exceptions: tuple = (Exception,)  # Exceptions that count as failures

@dataclass
class CircuitBreakerStats:
    """Circuit breaker statistics"""
    state: CircuitState
    failure_count: int
    success_count: int
    last_failure_time: Optional[datetime]
    last_success_time: Optional[datetime]
    total_requests: int
    total_failures: int
    total_successes: int
    uptime_percentage: float

class CircuitBreaker:
    """
    Production-grade circuit breaker for external dependencies
    
    Implements the circuit breaker pattern to prevent cascading failures
    and provide graceful degradation when services become unavailable.
    """
    
    def __init__(self, name: str, config: CircuitBreakerConfig = None):
        self.name = name
        self.config = config or CircuitBreakerConfig()
        
        # State management
        self.state = CircuitState.CLOSED
        self.failure_count = 0
        self.success_count = 0
        self.last_failure_time: Optional[datetime] = None
        self.last_success_time: Optional[datetime] = None
        
        # Statistics
        self.total_requests = 0
        self.total_failures = 0
        self.total_successes = 0
        self.state_change_time = datetime.now()
        
        # Callbacks
        self.on_state_change: Optional[Callable] = None
        self.on_failure: Optional[Callable] = None
        self.on_success: Optional[Callable] = None
        
        structured_logger.info(
            "Circuit breaker initialized",
            name=name,
            failure_threshold=self.config.failure_threshold,
            recovery_timeout=self.config.recovery_timeout
        )
    
    async def call(self, func: Callable[..., Awaitable], *args, **kwargs):
        """
        Execute function with circuit breaker protection
        
        Args:
            func: Async function to execute
            *args: Function arguments
            **kwargs: Function keyword arguments
            
        Returns:
            Function result
            
        Raises:
            CircuitBreakerOpenError: When circuit is open
            TimeoutError: When operation times out
            Exception: Original function exceptions
        """
        self.total_requests += 1
        
        # Check if circuit is open
        if self.state == CircuitState.OPEN:
            if self._should_attempt_reset():
                self._transition_to_half_open()
            else:
                self.total_failures += 1
                raise CircuitBreakerOpenError(
                    f"Circuit breaker '{self.name}' is OPEN. "
                    f"Next attempt in {self._time_until_retry():.1f}s"
                )
        
        try:
            # Execute with timeout
            result = await asyncio.wait_for(
                func(*args, **kwargs),
                timeout=self.config.timeout
            )
            
            # Success handling
            await self._on_success()
            return result
            
        except self.config.expected_exceptions as e:
            # Failure handling
            await self._on_failure(e)
            raise
        except asyncio.TimeoutError as e:
            # Timeout is also a failure
            await self._on_failure(e)
            raise TimeoutError(f"Operation timed out after {self.config.timeout}s")
    
    async def _on_success(self):
        """Handle successful operation"""
        self.total_successes += 1
        self.last_success_time = datetime.now()
        
        if self.state == CircuitState.HALF_OPEN:
            self.success_count += 1
            if self.success_count >= self.config.success_threshold:
                self._transition_to_closed()
        elif self.state == CircuitState.CLOSED:
            # Reset failure count on success
            self.failure_count = 0
        
        if self.on_success:
            await self.on_success(self.name)
    
    async def _on_failure(self, exception: Exception):
        """Handle failed operation"""
        self.total_failures += 1
        self.failure_count += 1
        self.last_failure_time = datetime.now()
        
        structured_logger.warning(
            "Circuit breaker failure",
            name=self.name,
            failure_count=self.failure_count,
            exception=str(exception),
            state=self.state.value
        )
        
        # Check if we should open the circuit
        if (self.state == CircuitState.CLOSED and 
            self.failure_count >= self.config.failure_threshold):
            self._transition_to_open()
        elif self.state == CircuitState.HALF_OPEN:
            # Any failure in half-open goes back to open
            self._transition_to_open()
        
        if self.on_failure:
            await self.on_failure(self.name, exception)
    
    def _should_attempt_reset(self) -> bool:
        """Check if enough time has passed to attempt reset"""
        if not self.last_failure_time:
            return True
        
        time_since_failure = datetime.now() - self.last_failure_time
        return time_since_failure.total_seconds() >= self.config.recovery_timeout
    
    def _time_until_retry(self) -> float:
        """Calculate seconds until next retry attempt"""
        if not self.last_failure_time:
            return 0.0
        
        time_since_failure = datetime.now() - self.last_failure_time
        return max(0.0, self.config.recovery_timeout - time_since_failure.total_seconds())
    
    def _transition_to_open(self):
        """Transition circuit to OPEN state"""
        old_state = self.state
        self.state = CircuitState.OPEN
        self.state_change_time = datetime.now()
        self.success_count = 0
        
        structured_logger.critical(
            "Circuit breaker OPENED",
            name=self.name,
            previous_state=old_state.value,
            failure_count=self.failure_count,
            recovery_timeout=self.config.recovery_timeout
        )
        
        if self.on_state_change:
            asyncio.create_task(self.on_state_change(self.name, old_state, self.state))
    
    def _transition_to_half_open(self):
        """Transition circuit to HALF_OPEN state"""
        old_state = self.state
        self.state = CircuitState.HALF_OPEN
        self.state_change_time = datetime.now()
        self.success_count = 0
        self.failure_count = 0
        
        structured_logger.info(
            "Circuit breaker HALF-OPEN",
            name=self.name,
            previous_state=old_state.value,
            testing_recovery=True
        )
        
        if self.on_state_change:
            asyncio.create_task(self.on_state_change(self.name, old_state, self.state))
    
    def _transition_to_closed(self):
        """Transition circuit to CLOSED state"""
        old_state = self.state
        self.state = CircuitState.CLOSED
        self.state_change_time = datetime.now()
        self.failure_count = 0
        self.success_count = 0
        
        structured_logger.info(
            "Circuit breaker CLOSED",
            name=self.name,
            previous_state=old_state.value,
            service_recovered=True
        )
        
        if self.on_state_change:
            asyncio.create_task(self.on_state_change(self.name, old_state, self.state))
    
    def get_stats(self) -> CircuitBreakerStats:
        """Get circuit breaker statistics"""
        total_operations = max(self.total_requests, 1)
        uptime_percentage = (self.total_successes / total_operations) * 100
        
        return CircuitBreakerStats(
            state=self.state,
            failure_count=self.failure_count,
            success_count=self.success_count,
            last_failure_time=self.last_failure_time,
            last_success_time=self.last_success_time,
            total_requests=self.total_requests,
            total_failures=self.total_failures,
            total_successes=self.total_successes,
            uptime_percentage=uptime_percentage
        )
    
    def is_available(self) -> bool:
        """Check if service is available (circuit not open)"""
        if self.state == CircuitState.OPEN:
            return self._should_attempt_reset()
        return True
    
    def force_open(self):
        """Manually force circuit open (for maintenance)"""
        structured_logger.warning(
            "Circuit breaker manually opened",
            name=self.name,
            reason="manual_override"
        )
        self._transition_to_open()
    
    def force_close(self):
        """Manually force circuit closed (for recovery)"""
        structured_logger.warning(
            "Circuit breaker manually closed",
            name=self.name,
            reason="manual_override"
        )
        self._transition_to_closed()

class CircuitBreakerOpenError(Exception):
    """Raised when circuit breaker is open"""
    pass

class CircuitBreakerManager:
    """
    Manages multiple circuit breakers for different services
    """
    
    def __init__(self):
        self.breakers: Dict[str, CircuitBreaker] = {}
        self.global_stats = {
            'total_breakers': 0,
            'open_breakers': 0,
            'half_open_breakers': 0,
            'closed_breakers': 0
        }
    
    def get_breaker(self, name: str, config: CircuitBreakerConfig = None) -> CircuitBreaker:
        """Get or create circuit breaker for service"""
        if name not in self.breakers:
            self.breakers[name] = CircuitBreaker(name, config)
            self.global_stats['total_breakers'] += 1
        
        return self.breakers[name]
    
    def get_all_stats(self) -> Dict[str, Any]:
        """Get statistics for all circuit breakers"""
        stats = {}
        
        open_count = 0
        half_open_count = 0
        closed_count = 0
        
        for name, breaker in self.breakers.items():
            breaker_stats = breaker.get_stats()
            stats[name] = breaker_stats
            
            if breaker_stats.state == CircuitState.OPEN:
                open_count += 1
            elif breaker_stats.state == CircuitState.HALF_OPEN:
                half_open_count += 1
            else:
                closed_count += 1
        
        self.global_stats.update({
            'open_breakers': open_count,
            'half_open_breakers': half_open_count,
            'closed_breakers': closed_count
        })
        
        return {
            'global_stats': self.global_stats,
            'breaker_stats': stats
        }
    
    def is_system_healthy(self) -> bool:
        """Check if overall system is healthy"""
        if not self.breakers:
            return True
        
        open_breakers = sum(1 for b in self.breakers.values() 
                          if b.state == CircuitState.OPEN)
        
        # System unhealthy if more than 50% of breakers are open
        return (open_breakers / len(self.breakers)) < 0.5

# Global circuit breaker manager
circuit_breaker_manager = CircuitBreakerManager()
