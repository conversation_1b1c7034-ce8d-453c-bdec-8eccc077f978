2025-07-29 23:33:48,252 - __main__ - INFO - Starting in full system mode
2025-07-29 23:33:48,256 - __main__ - INFO - Starting Options Manipulation Detection System
2025-07-29 23:33:48,256 - __main__ - INFO - Environment: development
2025-07-29 23:33:48,256 - __main__ - INFO - Monitoring symbols: ['NIFTY', 'BANKNIFTY', 'FINNIFTY']
2025-07-29 23:33:49,088 - api.main - INFO - Starting Options Manipulation Detection API
2025-07-29 23:33:49,093 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-29 23:33:49,094 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("manipulation_signals")
2025-07-29 23:33:49,094 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-29 23:33:49,095 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-29 23:33:49,096 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("manipulation_signals")
2025-07-29 23:33:49,096 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-29 23:33:49,100 - sqlalchemy.engine.Engine - INFO - PRAGMA temp.table_info("manipulation_signals")
2025-07-29 23:33:49,100 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-29 23:33:49,102 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("options_data")
2025-07-29 23:33:49,102 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-29 23:33:49,103 - sqlalchemy.engine.Engine - INFO - PRAGMA temp.table_info("options_data")
2025-07-29 23:33:49,103 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-29 23:33:49,104 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("system_metrics")
2025-07-29 23:33:49,104 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-29 23:33:49,106 - sqlalchemy.engine.Engine - INFO - PRAGMA temp.table_info("system_metrics")
2025-07-29 23:33:49,107 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-29 23:33:49,107 - sqlalchemy.engine.Engine - INFO - PRAGMA temp.table_info("manipulation_signals")
2025-07-29 23:33:49,108 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-29 23:33:49,109 - sqlalchemy.engine.Engine - INFO - 
CREATE TABLE manipulation_signals (
	id UUID NOT NULL, 
	pattern_type VARCHAR(50) NOT NULL, 
	timestamp DATETIME NOT NULL, 
	symbols_affected JSON NOT NULL, 
	confidence FLOAT NOT NULL, 
	description TEXT NOT NULL, 
	estimated_profit FLOAT NOT NULL, 
	market_impact JSON NOT NULL, 
	detection_algorithm VARCHAR(100) NOT NULL, 
	raw_data JSON, 
	created_at DATETIME, 
	PRIMARY KEY (id)
)


2025-07-29 23:33:49,110 - sqlalchemy.engine.Engine - INFO - [no key 0.00104s] ()
2025-07-29 23:33:49,111 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("options_data")
2025-07-29 23:33:49,111 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-29 23:33:49,112 - sqlalchemy.engine.Engine - INFO - PRAGMA temp.table_info("options_data")
2025-07-29 23:33:49,112 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-29 23:33:49,113 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("system_metrics")
2025-07-29 23:33:49,113 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-29 23:33:49,118 - sqlalchemy.engine.Engine - INFO - 
CREATE TABLE options_data (
	id INTEGER NOT NULL, 
	symbol VARCHAR(20) NOT NULL, 
	expiry_date DATETIME NOT NULL, 
	strike FLOAT NOT NULL, 
	option_type VARCHAR(2) NOT NULL, 
	last_price FLOAT, 
	bid_price FLOAT, 
	ask_price FLOAT, 
	volume INTEGER, 
	open_interest INTEGER, 
	bid_qty INTEGER, 
	ask_qty INTEGER, 
	delta FLOAT, 
	gamma FLOAT, 
	theta FLOAT, 
	vega FLOAT, 
	implied_volatility FLOAT, 
	timestamp DATETIME NOT NULL, 
	change FLOAT, 
	percent_change FLOAT, 
	PRIMARY KEY (id)
)


2025-07-29 23:33:49,119 - sqlalchemy.engine.Engine - INFO - [no key 0.00118s] ()
2025-07-29 23:33:49,140 - sqlalchemy.engine.Engine - INFO - 
CREATE TABLE system_metrics (
	id INTEGER NOT NULL, 
	timestamp DATETIME NOT NULL, 
	data_points_collected INTEGER, 
	collection_errors INTEGER, 
	collection_latency_ms FLOAT, 
	detection_cycles_completed INTEGER, 
	total_signals_generated INTEGER, 
	high_confidence_signals INTEGER, 
	memory_usage_mb FLOAT, 
	cpu_usage_percent FLOAT, 
	active_connections INTEGER, 
	PRIMARY KEY (id)
)


2025-07-29 23:33:49,141 - sqlalchemy.engine.Engine - INFO - [no key 0.00062s] ()
2025-07-29 23:33:49,145 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-29 23:33:49,146 - utils.database - INFO - Database manager initialized successfully
2025-07-29 23:33:49,156 - sqlalchemy.engine.Engine - INFO - 
CREATE TABLE manipulation_signals (
	id UUID NOT NULL, 
	pattern_type VARCHAR(50) NOT NULL, 
	timestamp DATETIME NOT NULL, 
	symbols_affected JSON NOT NULL, 
	confidence FLOAT NOT NULL, 
	description TEXT NOT NULL, 
	estimated_profit FLOAT NOT NULL, 
	market_impact JSON NOT NULL, 
	detection_algorithm VARCHAR(100) NOT NULL, 
	raw_data JSON, 
	created_at DATETIME, 
	PRIMARY KEY (id)
)


2025-07-29 23:33:49,157 - sqlalchemy.engine.Engine - INFO - [no key 0.00060s] ()
2025-07-29 23:33:49,159 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-29 23:33:49,160 - utils.database - ERROR - Failed to initialize database: (sqlite3.OperationalError) table manipulation_signals already exists
[SQL: 
CREATE TABLE manipulation_signals (
	id UUID NOT NULL, 
	pattern_type VARCHAR(50) NOT NULL, 
	timestamp DATETIME NOT NULL, 
	symbols_affected JSON NOT NULL, 
	confidence FLOAT NOT NULL, 
	description TEXT NOT NULL, 
	estimated_profit FLOAT NOT NULL, 
	market_impact JSON NOT NULL, 
	detection_algorithm VARCHAR(100) NOT NULL, 
	raw_data JSON, 
	created_at DATETIME, 
	PRIMARY KEY (id)
)

]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-29 23:33:49,160 - core.detection_engine - ERROR - Failed to initialize detection engine: (sqlite3.OperationalError) table manipulation_signals already exists
[SQL: 
CREATE TABLE manipulation_signals (
	id UUID NOT NULL, 
	pattern_type VARCHAR(50) NOT NULL, 
	timestamp DATETIME NOT NULL, 
	symbols_affected JSON NOT NULL, 
	confidence FLOAT NOT NULL, 
	description TEXT NOT NULL, 
	estimated_profit FLOAT NOT NULL, 
	market_impact JSON NOT NULL, 
	detection_algorithm VARCHAR(100) NOT NULL, 
	raw_data JSON, 
	created_at DATETIME, 
	PRIMARY KEY (id)
)

]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-29 23:33:49,161 - api.main - ERROR - Failed to initialize detection engine: (sqlite3.OperationalError) table manipulation_signals already exists
[SQL: 
CREATE TABLE manipulation_signals (
	id UUID NOT NULL, 
	pattern_type VARCHAR(50) NOT NULL, 
	timestamp DATETIME NOT NULL, 
	symbols_affected JSON NOT NULL, 
	confidence FLOAT NOT NULL, 
	description TEXT NOT NULL, 
	estimated_profit FLOAT NOT NULL, 
	market_impact JSON NOT NULL, 
	detection_algorithm VARCHAR(100) NOT NULL, 
	raw_data JSON, 
	created_at DATETIME, 
	PRIMARY KEY (id)
)

]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-29 23:33:49,401 - utils.cache - INFO - Cache manager initialized successfully
2025-07-29 23:33:49,401 - data_collectors.multi_source_collector - INFO - Initializing multi-source data collector...
2025-07-29 23:33:49,401 - data_collectors.nse_collector - INFO - Initializing NSE session...
2025-07-29 23:33:50,443 - data_collectors.nse_collector - INFO - Got 4 cookies from NSE homepage
2025-07-29 23:33:51,892 - data_collectors.nse_collector - INFO - Updated cookies from options page, total: 7
2025-07-29 23:33:51,892 - data_collectors.nse_collector - INFO - NSE data collector session started
2025-07-29 23:33:51,892 - data_collectors.multi_source_collector - INFO - ✅ NSE collector initialized
2025-07-29 23:33:51,894 - data_collectors.multi_source_collector - INFO - 🚀 Multi-source collector ready with sources: ['nse']
2025-07-29 23:33:51,902 - utils.metrics - INFO - Prometheus metrics server started on port 8000
2025-07-29 23:33:51,902 - core.detection_engine - INFO - Detection engine initialized successfully
2025-07-29 23:33:51,902 - __main__ - INFO - System started successfully
2025-07-29 23:33:51,902 - core.detection_engine - INFO - Starting continuous detection engine
2025-07-29 23:33:51,902 - utils.market_hours - INFO - Market CLOSED: Current time 23:33:51.902583 outside trading hours (09:15:00-15:30:00)
2025-07-29 23:33:51,903 - options_detection - WARNING - Detection skipped: Market closed: Outside trading hours (09:15:00-15:30:00 IST)
2025-07-29 23:33:51,903 - utils.market_hours - INFO - Market CLOSED: Current time 23:33:51.903584 outside trading hours (09:15:00-15:30:00)
2025-07-29 23:33:51,904 - utils.market_hours - INFO - Market CLOSED: Current time 23:33:51.904585 outside trading hours (09:15:00-15:30:00)
2025-07-29 23:33:51,904 - options_detection - INFO - Market status: {'current_time': '2025-07-29 23:33:51 IST', 'is_market_open': False, 'is_pre_market': False, 'is_after_market': False, 'is_weekend': False, 'is_holiday': False, 'next_market_open': '2025-07-30 09:15:00', 'trading_hours': '09:15:00 - 15:30:00 IST'}
2025-07-29 23:33:51,920 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-29 23:33:51,924 - sqlalchemy.engine.Engine - INFO - INSERT INTO system_metrics (timestamp, data_points_collected, collection_errors, collection_latency_ms, detection_cycles_completed, total_signals_generated, high_confidence_signals, memory_usage_mb, cpu_usage_percent, active_connections) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-29 23:33:51,924 - sqlalchemy.engine.Engine - INFO - [generated in 0.00067s] ('2025-07-29 18:03:51.913596', 0, 0, 0.0, 0, 0, 0, 153.18359375, 0.0, 0)
2025-07-29 23:33:51,927 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-29 23:34:45,073 - __main__ - INFO - Received signal 2, initiating shutdown
2025-07-29 23:34:45,074 - __main__ - INFO - Shutting down Options Manipulation Detection System
2025-07-29 23:34:45,074 - data_collectors.multi_source_collector - INFO - Shutting down multi-source data collector...
2025-07-29 23:34:45,074 - data_collectors.nse_collector - INFO - NSE data collector session closed
2025-07-29 23:34:45,074 - data_collectors.multi_source_collector - INFO - Multi-source data collector shutdown complete
2025-07-29 23:34:45,074 - utils.database - INFO - Database connections closed
2025-07-29 23:34:45,075 - utils.cache - INFO - Cache connection closed
2025-07-29 23:34:45,075 - core.detection_engine - INFO - Detection engine shutdown complete
2025-07-29 23:34:45,075 - __main__ - INFO - System shutdown complete
2025-07-29 23:34:51,942 - data_collectors.multi_source_collector - INFO - Shutting down multi-source data collector...
2025-07-29 23:34:51,943 - data_collectors.nse_collector - INFO - NSE data collector session closed
2025-07-29 23:34:51,943 - data_collectors.multi_source_collector - INFO - Multi-source data collector shutdown complete
2025-07-29 23:34:51,944 - utils.database - INFO - Database connections closed
2025-07-29 23:34:51,944 - utils.cache - INFO - Cache connection closed
2025-07-29 23:34:51,944 - core.detection_engine - INFO - Detection engine shutdown complete
