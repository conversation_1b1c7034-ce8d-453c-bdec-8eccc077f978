"""
Memory-Efficient Kill Switches
The Code Auditor's Directive #4: Fix All Memory Leaks

Replaces unbounded list growth with fixed-size circular buffers
Uses NumPy arrays for efficient vectorized calculations
Guarantees O(1) memory usage regardless of trading duration
"""
import numpy as np
from collections import deque
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Tuple, Optional, Union
import logging
from dataclasses import dataclass
from enum import Enum

from utils.structured_logging import structured_logger

logger = logging.getLogger(__name__)

class KillSwitchType(Enum):
    """Types of kill switches"""
    DRAWDOWN = "drawdown"
    CONSECUTIVE_LOSSES = "consecutive_losses"
    WIN_RATE = "win_rate"
    CONFIDENCE_DEGRADATION = "confidence_degradation"
    MANUAL_OVERRIDE = "manual_override"

@dataclass
class TradeResult:
    """Individual trade result"""
    timestamp: datetime
    symbol: str
    entry_price: float
    exit_price: float
    quantity: int
    pnl: float
    confidence: float
    was_winner: bool

@dataclass
class KillSwitchStatus:
    """Kill switch status"""
    is_triggered: bool
    trigger_type: Optional[KillSwitchType]
    trigger_reason: str
    current_drawdown: float
    consecutive_losses: int
    win_rate_30_trades: float
    confidence_weighted_win_rate: float
    trades_analyzed: int
    reset_required: bool

class MemoryEfficientKillSwitches:
    """
    Memory-efficient kill switches with O(1) memory usage
    The Code Auditor's Directive #4: Fixed-size circular buffers
    """
    
    def __init__(self, config: Dict = None, max_trades_in_memory: int = 10000):
        """
        Initialize memory-efficient kill switches
        
        Args:
            config: Configuration dictionary
            max_trades_in_memory: Maximum trades to keep in memory (fixed size)
        """
        self.config = config or {}
        self.max_trades_in_memory = max_trades_in_memory
        
        # Kill switch thresholds
        self.max_drawdown_pct = self.config.get('max_drawdown_pct', 15.0)  # 15%
        self.max_consecutive_losses = self.config.get('max_consecutive_losses', 7)
        self.min_win_rate_30_trades = self.config.get('min_win_rate_30_trades', 40.0)  # 40%
        self.min_confidence_weighted_win_rate = self.config.get('min_confidence_weighted_win_rate', 35.0)  # 35%
        
        # Analysis parameters
        self.min_trades_for_analysis = self.config.get('min_trades_for_analysis', 30)
        
        # CRITICAL: Fixed-size circular buffer (The Code Auditor's Directive #4)
        self.trade_history = deque(maxlen=max_trades_in_memory)
        
        # CRITICAL: NumPy arrays for efficient vectorized calculations
        self.pnl_array = np.zeros(max_trades_in_memory, dtype=np.float64)
        self.confidence_array = np.zeros(max_trades_in_memory, dtype=np.float64)
        self.winner_array = np.zeros(max_trades_in_memory, dtype=bool)
        self.timestamp_array = np.zeros(max_trades_in_memory, dtype=np.int64)
        
        # Circular buffer management
        self.current_index = 0
        self.total_trades = 0
        self.buffer_full = False
        
        # Equity tracking
        self.peak_equity = 0.0
        self.current_equity = 0.0
        
        # Kill switch state
        self.is_killed = False
        self.kill_reason = ""
        self.kill_timestamp: Optional[datetime] = None
        
        structured_logger.info(
            "MemoryEfficientKillSwitches initialized",
            max_trades_in_memory=max_trades_in_memory,
            max_drawdown_pct=self.max_drawdown_pct,
            max_consecutive_losses=self.max_consecutive_losses,
            memory_usage="O(1) - fixed size"
        )
    
    def add_trade_result(self, trade: TradeResult):
        """
        Add new trade result with O(1) memory usage
        
        Args:
            trade: Trade result to add
        """
        # Calculate array index (circular)
        idx = self.current_index % self.max_trades_in_memory
        
        # Update NumPy arrays for vectorized calculations
        self.pnl_array[idx] = trade.pnl
        self.confidence_array[idx] = trade.confidence
        self.winner_array[idx] = trade.was_winner
        self.timestamp_array[idx] = int(trade.timestamp.timestamp())
        
        # Update circular buffer
        self.trade_history.append(trade)
        
        # Update counters
        self.current_index += 1
        self.total_trades += 1
        
        if self.current_index >= self.max_trades_in_memory:
            self.buffer_full = True
        
        # Update equity tracking
        self.current_equity += trade.pnl
        if self.current_equity > self.peak_equity:
            self.peak_equity = self.current_equity
        
        # Check kill switches
        self._check_all_kill_switches()
        
        # Log memory usage periodically
        if self.total_trades % 1000 == 0:
            structured_logger.info(
                "Kill switches memory status",
                total_trades=self.total_trades,
                buffer_size=len(self.trade_history),
                memory_usage_mb=self._calculate_memory_usage_mb(),
                buffer_full=self.buffer_full
            )
    
    def _check_all_kill_switches(self):
        """Check all kill switch conditions using vectorized operations"""
        if self.is_killed:
            return
        
        # Need minimum trades for analysis
        if self.total_trades < self.min_trades_for_analysis:
            return
        
        # Check drawdown kill switch
        current_drawdown = self._calculate_current_drawdown()
        if current_drawdown > self.max_drawdown_pct:
            self._trigger_kill_switch(
                KillSwitchType.DRAWDOWN,
                f"Drawdown exceeded {self.max_drawdown_pct}% (current: {current_drawdown:.1f}%)"
            )
            return
        
        # Check consecutive losses kill switch
        consecutive_losses = self._count_consecutive_losses_vectorized()
        if consecutive_losses >= self.max_consecutive_losses:
            self._trigger_kill_switch(
                KillSwitchType.CONSECUTIVE_LOSSES,
                f"Consecutive losses exceeded {self.max_consecutive_losses} (current: {consecutive_losses})"
            )
            return
        
        # Check win rate kill switch (last 30 trades)
        win_rate = self._calculate_win_rate_vectorized(30)
        if win_rate < self.min_win_rate_30_trades:
            self._trigger_kill_switch(
                KillSwitchType.WIN_RATE,
                f"Win rate below {self.min_win_rate_30_trades}% (current: {win_rate:.1f}%)"
            )
            return
        
        # Check confidence-weighted win rate
        conf_weighted_win_rate = self._calculate_confidence_weighted_win_rate_vectorized()
        if conf_weighted_win_rate < self.min_confidence_weighted_win_rate:
            self._trigger_kill_switch(
                KillSwitchType.CONFIDENCE_DEGRADATION,
                f"Confidence-weighted win rate below {self.min_confidence_weighted_win_rate}% (current: {conf_weighted_win_rate:.1f}%)"
            )
            return
    
    def _calculate_current_drawdown(self) -> float:
        """Calculate current drawdown percentage"""
        if self.peak_equity <= 0:
            return 0.0
        
        drawdown = (self.peak_equity - self.current_equity) / self.peak_equity * 100
        return max(drawdown, 0.0)
    
    def _count_consecutive_losses_vectorized(self) -> int:
        """Count consecutive losses using vectorized operations"""
        if self.total_trades == 0:
            return 0
        
        # Get recent trades (up to buffer size)
        recent_count = min(self.total_trades, self.max_trades_in_memory)
        
        if self.buffer_full:
            # Buffer is full, need to handle wrap-around
            start_idx = self.current_index % self.max_trades_in_memory
            if start_idx == 0:
                recent_winners = self.winner_array
            else:
                recent_winners = np.concatenate([
                    self.winner_array[start_idx:],
                    self.winner_array[:start_idx]
                ])
        else:
            # Buffer not full, simple slice
            recent_winners = self.winner_array[:recent_count]
        
        # Count consecutive losses from the end
        consecutive_losses = 0
        for i in range(len(recent_winners) - 1, -1, -1):
            if not recent_winners[i]:  # Loss
                consecutive_losses += 1
            else:  # Win
                break
        
        return consecutive_losses
    
    def _calculate_win_rate_vectorized(self, lookback: int = 30) -> float:
        """Calculate win rate using vectorized operations"""
        if self.total_trades == 0:
            return 0.0
        
        # Get last N trades efficiently
        recent_count = min(lookback, self.total_trades, self.max_trades_in_memory)
        
        if self.buffer_full and recent_count == lookback:
            # Get last N trades with wrap-around
            start_idx = (self.current_index - lookback) % self.max_trades_in_memory
            if start_idx + lookback <= self.max_trades_in_memory:
                recent_winners = self.winner_array[start_idx:start_idx + lookback]
            else:
                recent_winners = np.concatenate([
                    self.winner_array[start_idx:],
                    self.winner_array[:lookback - (self.max_trades_in_memory - start_idx)]
                ])
        else:
            # Simple case - get last N trades
            start_idx = max(0, self.current_index - recent_count)
            end_idx = min(self.current_index, self.max_trades_in_memory)
            recent_winners = self.winner_array[start_idx:end_idx]
        
        if len(recent_winners) == 0:
            return 0.0
        
        return np.mean(recent_winners) * 100.0
    
    def _calculate_confidence_weighted_win_rate_vectorized(self) -> float:
        """Calculate confidence-weighted win rate using vectorized operations"""
        if self.total_trades == 0:
            return 0.0
        
        # Get recent data (last 30 trades or all if less)
        recent_count = min(30, self.total_trades, self.max_trades_in_memory)
        
        if self.buffer_full and recent_count == 30:
            start_idx = (self.current_index - 30) % self.max_trades_in_memory
            if start_idx + 30 <= self.max_trades_in_memory:
                recent_winners = self.winner_array[start_idx:start_idx + 30]
                recent_confidence = self.confidence_array[start_idx:start_idx + 30]
            else:
                recent_winners = np.concatenate([
                    self.winner_array[start_idx:],
                    self.winner_array[:30 - (self.max_trades_in_memory - start_idx)]
                ])
                recent_confidence = np.concatenate([
                    self.confidence_array[start_idx:],
                    self.confidence_array[:30 - (self.max_trades_in_memory - start_idx)]
                ])
        else:
            start_idx = max(0, self.current_index - recent_count)
            end_idx = min(self.current_index, self.max_trades_in_memory)
            recent_winners = self.winner_array[start_idx:end_idx]
            recent_confidence = self.confidence_array[start_idx:end_idx]
        
        if len(recent_winners) == 0:
            return 0.0
        
        # Vectorized confidence-weighted calculation
        weighted_wins = recent_winners.astype(float) * recent_confidence
        total_confidence = np.sum(recent_confidence)
        
        if total_confidence == 0:
            return 0.0
        
        return (np.sum(weighted_wins) / total_confidence) * 100.0
    
    def _trigger_kill_switch(self, switch_type: KillSwitchType, reason: str):
        """Trigger kill switch with logging"""
        self.is_killed = True
        self.kill_reason = reason
        self.kill_timestamp = datetime.now()
        
        structured_logger.critical(
            "KILL SWITCH TRIGGERED",
            switch_type=switch_type.value,
            reason=reason,
            current_equity=self.current_equity,
            peak_equity=self.peak_equity,
            total_trades=self.total_trades,
            current_drawdown=self._calculate_current_drawdown()
        )
    
    def get_kill_switch_status(self) -> KillSwitchStatus:
        """Get current kill switch status"""
        return KillSwitchStatus(
            is_triggered=self.is_killed,
            trigger_type=KillSwitchType(self.kill_reason.split()[0].lower()) if self.is_killed else None,
            trigger_reason=self.kill_reason,
            current_drawdown=self._calculate_current_drawdown(),
            consecutive_losses=self._count_consecutive_losses_vectorized(),
            win_rate_30_trades=self._calculate_win_rate_vectorized(30),
            confidence_weighted_win_rate=self._calculate_confidence_weighted_win_rate_vectorized(),
            trades_analyzed=min(self.total_trades, self.max_trades_in_memory),
            reset_required=self.is_killed
        )
    
    def reset_kill_switches(self):
        """Reset kill switches (manual override)"""
        self.is_killed = False
        self.kill_reason = ""
        self.kill_timestamp = None
        
        structured_logger.warning(
            "Kill switches manually reset",
            total_trades=self.total_trades,
            current_equity=self.current_equity
        )
    
    def _calculate_memory_usage_mb(self) -> float:
        """Calculate approximate memory usage in MB"""
        # NumPy arrays
        array_bytes = (
            self.pnl_array.nbytes +
            self.confidence_array.nbytes +
            self.winner_array.nbytes +
            self.timestamp_array.nbytes
        )
        
        # Deque overhead (approximate)
        deque_bytes = len(self.trade_history) * 200  # Rough estimate per TradeResult
        
        total_bytes = array_bytes + deque_bytes
        return total_bytes / (1024 * 1024)  # Convert to MB
    
    def get_performance_summary(self) -> Dict:
        """Get performance summary with memory-efficient calculations"""
        if self.total_trades == 0:
            return {}
        
        # Use vectorized calculations for performance metrics
        recent_count = min(self.total_trades, self.max_trades_in_memory)
        
        if self.buffer_full:
            recent_pnl = self.pnl_array
            recent_winners = self.winner_array
        else:
            recent_pnl = self.pnl_array[:recent_count]
            recent_winners = self.winner_array[:recent_count]
        
        winner_pnl = recent_pnl[recent_winners]
        loser_pnl = recent_pnl[~recent_winners]
        
        return {
            "total_trades": self.total_trades,
            "trades_in_memory": len(self.trade_history),
            "memory_usage_mb": self._calculate_memory_usage_mb(),
            "total_pnl": float(np.sum(recent_pnl)),
            "current_equity": self.current_equity,
            "peak_equity": self.peak_equity,
            "current_drawdown_pct": self._calculate_current_drawdown(),
            "win_rate_pct": self._calculate_win_rate_vectorized(),
            "avg_winner": float(np.mean(winner_pnl)) if len(winner_pnl) > 0 else 0,
            "avg_loser": float(np.mean(loser_pnl)) if len(loser_pnl) > 0 else 0,
            "consecutive_losses": self._count_consecutive_losses_vectorized(),
            "confidence_weighted_win_rate": self._calculate_confidence_weighted_win_rate_vectorized(),
            "is_killed": self.is_killed,
            "kill_reason": self.kill_reason,
            "kill_timestamp": self.kill_timestamp,
            "buffer_full": self.buffer_full
        }

# Global memory-efficient kill switches instance
memory_efficient_kill_switches = MemoryEfficientKillSwitches()
