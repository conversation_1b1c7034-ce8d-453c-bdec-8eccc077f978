"""
Structured Logging Configuration
The Architect's Fix #5: Proper J<PERSON> logging with machine-parsable output
"""
import sys
import json
import logging
import traceback
from datetime import datetime
from typing import Any, Dict, Optional
from pathlib import Path

try:
    import structlog
    STRUCTLOG_AVAILABLE = True
except ImportError:
    STRUCTLOG_AVAILABLE = False

class JSONFormatter(logging.Formatter):
    """
    Custom JSON formatter for when structlog is not available
    The Architect's requirement: All logs as JSO<PERSON> with timestamp, level, module, message
    """
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record as JSON"""
        log_entry = {
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "level": record.levelname,
            "module": record.name,
            "message": record.getMessage(),
            "function": record.funcName,
            "line": record.lineno,
            "thread": record.thread,
            "process": record.process
        }
        
        # Add exception info if present
        if record.exc_info:
            log_entry["exception"] = {
                "type": record.exc_info[0].__name__ if record.exc_info[0] else None,
                "message": str(record.exc_info[1]) if record.exc_info[1] else None,
                "traceback": traceback.format_exception(*record.exc_info)
            }
        
        # Add extra fields if present
        if hasattr(record, 'extra_fields'):
            log_entry.update(record.extra_fields)
            
        return json.dumps(log_entry, default=str)

class StructuredLogger:
    """
    Structured logger that outputs JSON
    The Architect's mandate: Machine-parsable logs, not messy diary entries
    """
    
    def __init__(self, name: str = "options_detection"):
        self.name = name
        self.logger = self._setup_logger()
    
    def _setup_logger(self) -> logging.Logger:
        """Setup structured logger with JSON output"""
        logger = logging.getLogger(self.name)
        logger.setLevel(logging.INFO)
        
        # Remove existing handlers to avoid duplicates
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)
        
        # Create console handler with JSON formatter
        handler = logging.StreamHandler(sys.stdout)
        handler.setFormatter(JSONFormatter())
        logger.addHandler(handler)
        
        # Prevent propagation to avoid duplicate logs
        logger.propagate = False
        
        return logger
    
    def _log(self, level: str, message: str, **kwargs):
        """Internal logging method with structured data"""
        extra_fields = kwargs.copy()
        
        # Create a custom LogRecord with extra fields
        record = self.logger.makeRecord(
            name=self.logger.name,
            level=getattr(logging, level.upper()),
            fn="",
            lno=0,
            msg=message,
            args=(),
            exc_info=None
        )
        record.extra_fields = extra_fields
        
        self.logger.handle(record)
    
    def info(self, message: str, **kwargs):
        """Log info message with structured data"""
        self._log("info", message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        """Log warning message with structured data"""
        self._log("warning", message, **kwargs)
    
    def error(self, message: str, **kwargs):
        """Log error message with structured data"""
        self._log("error", message, **kwargs)
    
    def critical(self, message: str, **kwargs):
        """Log critical message with structured data"""
        self._log("critical", message, **kwargs)
    
    def debug(self, message: str, **kwargs):
        """Log debug message with structured data"""
        self._log("debug", message, **kwargs)
    
    def exception(self, message: str, **kwargs):
        """Log exception with full traceback"""
        import sys
        exc_info = sys.exc_info()
        
        extra_fields = kwargs.copy()
        if exc_info[0]:
            extra_fields["exception"] = {
                "type": exc_info[0].__name__,
                "message": str(exc_info[1]),
                "traceback": traceback.format_exception(*exc_info)
            }
        
        self._log("error", message, **extra_fields)

def setup_structured_logging(log_level: str = "INFO") -> StructuredLogger:
    """
    Setup structured logging for the entire application
    The Architect's Fix #5: JSON logs with proper structure
    """
    # Disable default logging to avoid console spam
    logging.getLogger().handlers.clear()
    
    # Create structured logger
    structured_logger = StructuredLogger("options_detection")
    
    # Set log level
    structured_logger.logger.setLevel(getattr(logging, log_level.upper()))
    
    # Log initialization
    structured_logger.info(
        "Structured logging initialized",
        log_level=log_level,
        structlog_available=STRUCTLOG_AVAILABLE,
        output_format="JSON"
    )
    
    return structured_logger

def log_system_startup(logger: StructuredLogger, config: Dict[str, Any]):
    """Log system startup with configuration"""
    logger.info(
        "System startup initiated",
        environment=config.get("environment", "unknown"),
        symbols=config.get("symbols", []),
        detection_interval=config.get("detection_interval", 0),
        database_url=config.get("database_url", "").replace("password", "***") if config.get("database_url") else None
    )

def log_fatal_error(logger: StructuredLogger, component: str, error: Exception):
    """
    Log fatal error and terminate application
    The Architect's Fix #3: Fail-fast with proper logging
    """
    logger.critical(
        "FATAL ERROR - TERMINATING APPLICATION",
        component=component,
        error_type=type(error).__name__,
        error_message=str(error),
        action="SYSTEM_SHUTDOWN"
    )
    
    # Log the full traceback
    logger.exception("Fatal error traceback")
    
    # Terminate application
    sys.exit(1)

def log_database_operation(logger: StructuredLogger, operation: str, table: str, 
                          records_affected: int = 0, duration_ms: float = 0.0, 
                          success: bool = True):
    """Log database operations with metrics"""
    logger.info(
        "Database operation completed",
        operation=operation,
        table=table,
        records_affected=records_affected,
        duration_ms=duration_ms,
        success=success
    )

def log_api_request(logger: StructuredLogger, method: str, endpoint: str, 
                   status_code: int, duration_ms: float, client_ip: str = None):
    """Log API requests with metrics"""
    logger.info(
        "API request processed",
        method=method,
        endpoint=endpoint,
        status_code=status_code,
        duration_ms=duration_ms,
        client_ip=client_ip
    )

def log_detection_cycle(logger: StructuredLogger, cycle_number: int, signals_found: int, 
                       duration_ms: float, symbols_processed: int):
    """Log detection cycle completion"""
    logger.info(
        "Detection cycle completed",
        cycle_number=cycle_number,
        signals_found=signals_found,
        duration_ms=duration_ms,
        symbols_processed=symbols_processed
    )

def log_manipulation_signal(logger: StructuredLogger, signal_data: Dict[str, Any]):
    """Log manipulation signal detection with evidence"""
    logger.warning(
        "MANIPULATION SIGNAL DETECTED",
        pattern_type=signal_data.get("pattern_type"),
        symbols_affected=signal_data.get("symbols_affected"),
        oi_change_pct=signal_data.get("oi_change_pct_1min"),
        iv_spike_z_score=signal_data.get("iv_spike_z_score"),
        bid_ask_spread_pct=signal_data.get("bid_ask_spread_pct"),
        volume_vs_avg=signal_data.get("traded_volume_vs_avg"),
        order_book_imbalance=signal_data.get("order_book_imbalance_ratio"),
        price_change_bps=signal_data.get("price_change_bps"),
        time_window_seconds=signal_data.get("time_window_seconds")
    )

# Global structured logger instance
structured_logger = setup_structured_logging()
