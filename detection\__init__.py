"""
Detection Module for Options Manipulation Detection System

This module contains all manipulation detection algorithms and the base framework
for implementing new detectors.
"""

from .base_detector import BaseDetector, detector_registry
from .vectorized_spoofing_detector import VectorizedSpoofingDetector

__all__ = [
    'BaseDetector',
    'detector_registry',
    'VectorizedSpoofingDetector'
]
