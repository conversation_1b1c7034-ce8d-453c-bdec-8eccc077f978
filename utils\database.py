"""
Database utilities with connection pooling and async support
"""
import asyncio
from contextlib import asynccontextmanager
from typing import List, Dict, Any, Optional, AsyncGenerator
import logging
from datetime import datetime

import asyncpg
from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import QueuePool

from config.settings import settings
from models.data_models import (
    Base, ManipulationSignalDB, OptionsDataDB, SystemMetricsDB,
    ManipulationSignal, OptionsData, PatternType, OptionType
)
from utils.circuit_breaker import circuit_breaker_manager, CircuitBreakerConfig, CircuitBreakerOpenError
from utils.exceptions import DatabaseError

logger = logging.getLogger(__name__)

class DatabaseManager:
    """
    Async database manager with connection pooling
    """
    
    def __init__(self):
        self.async_engine = None
        self.sync_engine = None
        self.async_session_factory = None
        self.sync_session_factory = None
        self._initialized = False

        # Circuit breaker for database resilience
        self.circuit_breaker = circuit_breaker_manager.get_breaker(
            "database",
            CircuitBreakerConfig(
                failure_threshold=3,
                recovery_timeout=30.0,
                success_threshold=2,
                timeout=10.0,
                expected_exceptions=(Exception,)
            )
        )
    
    async def initialize(self):
        """Initialize database connections and create tables"""
        if self._initialized:
            return
        
        try:
            # Create async engine
            db_url = settings.database.url
            if db_url.startswith("postgresql://"):
                db_url = db_url.replace("postgresql://", "postgresql+asyncpg://")
            elif db_url.startswith("sqlite://"):
                db_url = db_url.replace("sqlite://", "sqlite+aiosqlite://")

            if db_url.startswith("sqlite"):
                # SQLite doesn't support connection pooling parameters
                self.async_engine = create_async_engine(
                    db_url,
                    echo=settings.debug
                )
            else:
                # PostgreSQL with connection pooling
                self.async_engine = create_async_engine(
                    db_url,
                    pool_size=settings.database.pool_size,
                    max_overflow=settings.database.max_overflow,
                    pool_timeout=settings.database.pool_timeout,
                    echo=settings.debug
                )
            
            # Create sync engine for migrations
            sync_url = settings.database.url
            if sync_url.startswith("sqlite"):
                self.sync_engine = create_engine(
                    sync_url,
                    echo=settings.debug
                )
            else:
                self.sync_engine = create_engine(
                    sync_url,
                    pool_size=settings.database.pool_size,
                    max_overflow=settings.database.max_overflow,
                    pool_timeout=settings.database.pool_timeout,
                    poolclass=QueuePool,
                    echo=settings.debug
                )
            
            # Create session factories
            self.async_session_factory = async_sessionmaker(
                self.async_engine,
                class_=AsyncSession,
                expire_on_commit=False
            )
            
            self.sync_session_factory = sessionmaker(
                self.sync_engine,
                expire_on_commit=False
            )
            
            # Create tables with idempotent initialization (checkfirst=True)
            async with self.async_engine.begin() as conn:
                await conn.run_sync(lambda sync_conn: Base.metadata.create_all(sync_conn, checkfirst=True))

            self._initialized = True
            logger.info("Database manager initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize database: {str(e)}")
            # Use circuit breaker instead of killing application
            self.circuit_breaker.force_open()
            raise DatabaseError(f"Database initialization failed: {str(e)}")
    
    async def close(self):
        """Close database connections"""
        if self.async_engine:
            await self.async_engine.dispose()
        if self.sync_engine:
            self.sync_engine.dispose()
        
        self._initialized = False
        logger.info("Database connections closed")
    
    @asynccontextmanager
    async def get_session(self) -> AsyncGenerator[AsyncSession, None]:
        """
        Get async database session
        
        Yields:
            AsyncSession instance
        """
        if not self._initialized:
            await self.initialize()
        
        async with self.async_session_factory() as session:
            try:
                yield session
                await session.commit()
            except Exception:
                await session.rollback()
                raise
            finally:
                await session.close()
    
    async def store_manipulation_signals(self, signals: List[ManipulationSignal]):
        """
        Store manipulation signals in database with circuit breaker protection

        Args:
            signals: List of manipulation signals to store
        """
        if not signals:
            return

        try:
            await self.circuit_breaker.call(self._store_signals_impl, signals)
        except CircuitBreakerOpenError as e:
            logger.warning(f"Database circuit breaker open, signals not stored: {e}")
            raise DatabaseError("Database unavailable") from e

    async def _store_signals_impl(self, signals: List[ManipulationSignal]):
        """Implementation of signal storage with proper error handling"""
        async with self.get_session() as session:
            db_signals = []
            for signal in signals:
                db_signal = ManipulationSignalDB(
                    pattern_type=signal.pattern_type.value,
                    timestamp=signal.timestamp,
                    symbols_affected=signal.symbols_affected,
                    confidence=signal.confidence,
                    description=signal.description,
                    estimated_profit=signal.estimated_profit,
                    market_impact=signal.market_impact,
                    detection_algorithm=signal.detection_algorithm,
                    raw_data=signal.raw_data
                )
                db_signals.append(db_signal)

            session.add_all(db_signals)
            await session.flush()

            logger.info(f"Stored {len(signals)} manipulation signals")
    
    async def store_options_data(self, options_data: List[OptionsData]):
        """
        Store options data in database with circuit breaker protection

        Args:
            options_data: List of options data to store
        """
        if not options_data:
            return

        try:
            await self.circuit_breaker.call(self._store_options_impl, options_data)
        except CircuitBreakerOpenError as e:
            logger.warning(f"Database circuit breaker open, options data not stored: {e}")
            raise DatabaseError("Database unavailable") from e

    async def _store_options_impl(self, options_data: List[OptionsData]):
        
        async with self.get_session() as session:
            try:
                db_options = []
                for option in options_data:
                    db_option = OptionsDataDB(
                        symbol=option.symbol,
                        expiry_date=option.expiry_date,
                        strike=option.strike,
                        option_type=option.option_type.value,
                        last_price=option.last_price,
                        bid_price=option.bid_price,
                        ask_price=option.ask_price,
                        volume=option.volume,
                        open_interest=option.open_interest,
                        bid_qty=option.bid_qty,
                        ask_qty=option.ask_qty,
                        delta=option.delta,
                        gamma=option.gamma,
                        theta=option.theta,
                        vega=option.vega,
                        implied_volatility=option.implied_volatility,
                        timestamp=option.timestamp,
                        change=option.change,
                        percent_change=option.percent_change
                    )
                    db_options.append(db_option)
                
                session.add_all(db_options)
                await session.flush()
                
                logger.info(f"Stored {len(options_data)} options data points")
                
            except Exception as e:
                logger.error(f"Error storing options data: {str(e)}")
                raise
    
    async def get_recent_options_data(
        self, 
        symbol: str, 
        hours: int = 1
    ) -> List[OptionsData]:
        """
        Get recent options data for a symbol
        
        Args:
            symbol: Symbol to query
            hours: Number of hours of data to retrieve
            
        Returns:
            List of options data
        """
        async with self.get_session() as session:
            try:
                from sqlalchemy import select
                from datetime import timedelta

                cutoff_time = datetime.utcnow() - timedelta(hours=hours)
                
                stmt = select(OptionsDataDB).where(
                    OptionsDataDB.symbol == symbol,
                    OptionsDataDB.timestamp >= cutoff_time
                ).order_by(OptionsDataDB.timestamp.desc())
                
                result = await session.execute(stmt)
                db_options = result.scalars().all()
                
                # Convert to OptionsData objects
                options_data = []
                for db_option in db_options:
                    option = OptionsData(
                        symbol=db_option.symbol,
                        expiry_date=db_option.expiry_date,
                        strike=db_option.strike,
                        option_type=OptionType(db_option.option_type),
                        last_price=db_option.last_price,
                        bid_price=db_option.bid_price,
                        ask_price=db_option.ask_price,
                        volume=db_option.volume,
                        open_interest=db_option.open_interest,
                        bid_qty=db_option.bid_qty,
                        ask_qty=db_option.ask_qty,
                        delta=db_option.delta,
                        gamma=db_option.gamma,
                        theta=db_option.theta,
                        vega=db_option.vega,
                        implied_volatility=db_option.implied_volatility,
                        timestamp=db_option.timestamp,
                        change=db_option.change,
                        percent_change=db_option.percent_change
                    )
                    options_data.append(option)
                
                return options_data
                
            except Exception as e:
                logger.error(f"Error retrieving options data: {str(e)}")
                raise
    
    async def get_manipulation_signals(
        self, 
        hours: int = 24,
        min_confidence: float = 0.0
    ) -> List[ManipulationSignal]:
        """
        Get recent manipulation signals
        
        Args:
            hours: Number of hours to look back
            min_confidence: Minimum confidence threshold
            
        Returns:
            List of manipulation signals
        """
        async with self.get_session() as session:
            try:
                from sqlalchemy import select
                from datetime import timedelta
                
                cutoff_time = datetime.utcnow() - timedelta(hours=hours)
                
                stmt = select(ManipulationSignalDB).where(
                    ManipulationSignalDB.timestamp >= cutoff_time,
                    ManipulationSignalDB.confidence >= min_confidence
                ).order_by(ManipulationSignalDB.timestamp.desc())
                
                result = await session.execute(stmt)
                db_signals = result.scalars().all()
                
                # Convert to ManipulationSignal objects
                signals = []
                for db_signal in db_signals:
                    signal = ManipulationSignal(
                        id=str(db_signal.id),
                        pattern_type=PatternType(db_signal.pattern_type),
                        timestamp=db_signal.timestamp,
                        symbols_affected=db_signal.symbols_affected,
                        confidence=db_signal.confidence,
                        description=db_signal.description,
                        estimated_profit=db_signal.estimated_profit,
                        market_impact=db_signal.market_impact,
                        detection_algorithm=db_signal.detection_algorithm,
                        raw_data=db_signal.raw_data or {}
                    )
                    signals.append(signal)
                
                return signals
                
            except Exception as e:
                logger.error(f"Error retrieving manipulation signals: {str(e)}")
                raise
    
    async def store_system_metrics(self, metrics: Dict[str, Any]):
        """
        Store system performance metrics
        
        Args:
            metrics: Dictionary of metrics to store
        """
        async with self.get_session() as session:
            try:
                db_metrics = SystemMetricsDB(
                    timestamp=datetime.utcnow(),
                    data_points_collected=metrics.get("data_points_collected", 0),
                    collection_errors=metrics.get("collection_errors", 0),
                    collection_latency_ms=metrics.get("collection_latency_ms", 0.0),
                    detection_cycles_completed=metrics.get("detection_cycles_completed", 0),
                    total_signals_generated=metrics.get("total_signals_generated", 0),
                    high_confidence_signals=metrics.get("high_confidence_signals", 0),
                    memory_usage_mb=metrics.get("memory_usage_mb", 0.0),
                    cpu_usage_percent=metrics.get("cpu_usage_percent", 0.0),
                    active_connections=metrics.get("active_connections", 0)
                )
                
                session.add(db_metrics)
                await session.flush()
                
                logger.debug("Stored system metrics")
                
            except Exception as e:
                logger.error(f"Error storing system metrics: {str(e)}")
                raise
    
    async def cleanup_old_data(self, days: int = 30):
        """
        Clean up old data from database
        
        Args:
            days: Number of days of data to keep
        """
        async with self.get_session() as session:
            try:
                from sqlalchemy import delete
                from datetime import timedelta
                
                cutoff_time = datetime.utcnow() - timedelta(days=days)
                
                # Clean up old options data
                options_stmt = delete(OptionsDataDB).where(
                    OptionsDataDB.timestamp < cutoff_time
                )
                options_result = await session.execute(options_stmt)
                
                # Clean up old system metrics
                metrics_stmt = delete(SystemMetricsDB).where(
                    SystemMetricsDB.timestamp < cutoff_time
                )
                metrics_result = await session.execute(metrics_stmt)
                
                await session.commit()
                
                logger.info(
                    f"Cleaned up {options_result.rowcount} options records "
                    f"and {metrics_result.rowcount} metrics records older than {days} days"
                )
                
            except Exception as e:
                logger.error(f"Error cleaning up old data: {str(e)}")
                raise

# Global database manager instance
db_manager = DatabaseManager()
