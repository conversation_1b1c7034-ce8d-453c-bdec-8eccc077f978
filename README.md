# Options Manipulation Detection & Front-Running System

**A production-ready system for detecting institutional flow in Indian options markets and executing front-running strategies.**

Built based on The Architect's brutal critique to address real market conditions with quantitative risk management.

## 🎯 What This System Does

**Detects Real Institutional Flow** → **Front-Runs the Flow** → **Manages Risk Mechanically**

1. **Monitors NSE options data** in real-time for spoofing patterns
2. **Identifies institutional flow direction** hidden behind the spoofs
3. **Executes front-running trades** WITH the institutions (not against them)
4. **Manages risk mechanically** with 0.5% position sizing and kill switches

## ✅ The Architect's Fixes Implemented

### 🎯 Front-Running Logic (Not Counter-Spoofing)
- **Institutional Flow Detection**: Bid spoofing → Bearish flow → SELL
- **Flow Strength Filtering**: Only trade signals >30% institutional strength
- **Directional Trading**: Trade WITH the flow, not against the spoof
- **Conservative Sizing**: Maximum 0.5% risk per trade

### ⚡ End-to-End Latency Measurement
- **Complete Pipeline Tracking**: Market data → Signal → Order submission
- **Realistic Thresholds**: 800ms total pipeline (not fantasy 100ms)
- **Bottleneck Detection**: Identifies where delays occur
- **API Latency Monitoring**: NSE API calls and processing delays

### 💰 Realistic Execution Modeling
- **NSE Spread Simulation**: Actual bid-ask spreads based on market conditions
- **Partial Fills**: 70% full fill, 20% partial, 10% rejection rates
- **Market Impact**: Slippage scales with order size and liquidity
- **Execution Delays**: 50-1000ms realistic execution times

### 🛡️ Mechanical Risk Management
- **Multiple Kill Switches**: 15% drawdown OR 7 consecutive losses
- **Daily Trade Limits**: Maximum 10 trades per day
- **Liquidity Requirements**: Minimum volume thresholds
- **Breakeven Calculation**: Realistic 85%+ win rate needed

## 🚀 Quick Start

### Prerequisites
- Python 3.11+
- PostgreSQL 15+ (for data storage)
- Redis 7+ (for caching)

### Installation

1. **Install dependencies**
```bash
pip install -r requirements.txt
```

2. **Start databases** (using Docker)
```bash
# PostgreSQL
docker run -d --name postgres \
  -e POSTGRES_DB=options_db \
  -e POSTGRES_USER=options_user \
  -e POSTGRES_PASSWORD=options_password \
  -p 5432:5432 postgres:15-alpine

# Redis
docker run -d --name redis -p 6379:6379 redis:7-alpine
```

3. **Run the system**
```bash
# Full system (detection + API)
python main.py --mode full

# Detection only
python main.py --mode detection

# API only
python main.py --mode api
```

## ⚙️ Configuration

The system is configured via `config/settings.py`. Key settings:

```python
# Symbols to monitor
SYMBOLS = ["NIFTY", "BANKNIFTY", "FINNIFTY"]

# Detection thresholds (adaptive)
SPOOF_QTY_THRESHOLD = 1000
HIGH_CONFIDENCE_THRESHOLD = 0.8

# Risk management
MAX_POSITION_SIZE = 0.005  # 0.5% of capital
MAX_DAILY_TRADES = 10
STOP_LOSS_PERCENT = 0.10

# Database
DB_URL = "postgresql://user:password@localhost:5432/options_db"
REDIS_HOST = "localhost"
```

## 📊 API Usage

### System Status
```bash
curl "http://localhost:8080/system/status"
```

### Recent Signals
```bash
curl "http://localhost:8080/signals?hours=24&min_confidence=0.8"
```

### Paper Trading Performance
```bash
curl "http://localhost:8080/paper-trading/performance"
```

### Health Check
```bash
curl "http://localhost:8080/health"
```

## 📈 Monitoring

### Key Metrics
- **Latency**: End-to-end pipeline timing
- **Signals**: Detection rate and confidence scores
- **P&L**: Paper trading performance
- **Risk**: Position sizing and kill switch status

### Logs
```bash
# View real-time logs
tail -f logs/options_detection.log
```

## 🔍 Detection Algorithms

### 1. Spoofing Detection
**What it detects**: Large orders placed and quickly cancelled to manipulate prices
- Monitors bid/ask quantity spikes and removals
- Measures price impact and timing
- Confidence scoring based on quantity size

### 2. Institutional Flow Detection
**What it detects**: Real FII/DII institutional flow hidden behind spoofs
- Analyzes volume patterns and open interest
- Identifies near-ATM options with high notional value
- Calculates flow strength and direction

### 3. Front-Running Logic
**How it trades**: WITH the institutional flow, not against the spoof
- Bid spoofing detected → Bearish institutional flow → SELL action
- Ask spoofing detected → Bullish institutional flow → BUY action
- Only trades signals with >30% flow strength

## 📡 Data Sources

### NSE APIs
- **Options Chain**: Real-time options data
- **Market Status**: Trading hours validation
- **Rate Limiting**: 60 requests/minute with retry logic

## 🏗️ System Architecture

```
options-manipulation-detection/
├── api/                    # REST API endpoints
├── config/                 # Configuration management
├── core/                   # Core detection engine
├── data_collectors/        # NSE data collection
├── detection/              # Detection algorithms
│   ├── spoofing_detector.py
│   └── indian_institutional_flow_detector.py
├── models/                 # Data models
├── paper_trading/          # Front-running paper trading
├── risk_management/        # Kill switches and risk controls
├── utils/                  # Utilities (latency, caching, etc.)
├── main.py                 # Application entry point
└── requirements.txt        # Dependencies
```

## 🧪 Testing

### Run Validation Tests
```bash
# Test all Architect fixes
python test_final_validation.py
```

### Expected Results
```
✅ Confidence scoring system validated
✅ Market impact analysis completed
✅ Position sizing framework established
✅ Integration analysis performed
```

## 🚀 Production Deployment

### Docker Deployment
```bash
# Build and run
docker-compose up -d

# View logs
docker-compose logs -f
```

### Production Checklist
- ✅ Configure proper database credentials
- ✅ Set up SSL/TLS for API endpoints
- ✅ Configure alerting for high-confidence signals
- ✅ Set up automated database backups
- ✅ Monitor system performance and latency

## 🛠️ Troubleshooting

### Common Issues

**Database Connection Errors**
```bash
docker-compose logs postgres
```

**NSE API Rate Limiting**
```bash
curl "http://localhost:8080/system/status"
```

**High Memory Usage**
```bash
curl "http://localhost:8080/system/status"
```

## ⚠️ Disclaimer

**This system is for educational and research purposes only.**

- Always comply with applicable laws and regulations
- The authors are not responsible for any misuse
- Paper trading results do not guarantee real trading performance
- Past performance does not indicate future results

## 🎯 The Architect's Verdict

**From:** *"You've built a technically sophisticated surveillance system masquerading as a trading strategy"*

**To:** *"A purpose-built front-running system that exploits institutional flow detection with proper risk management"*

### Key Transformations:
1. **Trading Logic**: From naive counter-spoofing → Institutional flow front-running
2. **Latency Measurement**: From internal code timing → End-to-end pipeline tracking
3. **Backtesting**: From fantasy execution → Realistic NSE market simulation
4. **Position Sizing**: From 10% risk → 0.5% risk with validation
5. **Risk Management**: From hope-based → Quantitative multi-layer protection

**The system now embodies The Architect's core principle: "The edge is in the information AND the execution."**
